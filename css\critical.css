/* CSS حرج للتحميل السريع - يحتوي على الأنماط الأساسية فقط */

/* Font Definitions */
@font-face {
    font-family: 'NotoNaskhArabic';
    src: url('../font/NotoNaskhArabic-Medium.ttf') format('truetype');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'NotoNaskhArabic';
    src: url('../font/NotoNaskhArabic-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'NotoNaskhArabic';
    src: url('../font/NotoNaskhArabic-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

/* إعادة تعيين أساسية */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'NotoNaskhArabic', 'Arial', sans-serif;
}

:root {
    --primary-color: #28909A;
    --secondary-color: #1c5c62;
    --accent-color-1: #F7A600;
    --accent-color-2: #008B80;
    --text-color: #ffffff;
    --bg-dark: #0f0f23;
    --card-bg: rgba(255, 255, 255, 0.05);
    --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

body {
    font-family: 'NotoNaskhArabic', 'Arial', sans-serif;
    background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
    color: var(--text-color);
    overflow-x: hidden;
    line-height: 1.6;
}

/* شاشة التحميل */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 1;
    visibility: visible;
    transition: opacity 0.8s ease, visibility 0.8s ease;
}

.page-loader.fade-out {
    opacity: 0;
    visibility: hidden;
}

.loader-logo {
    text-align: center;
    animation: logoFloat 2s ease-in-out infinite;
}

.loader-logo img {
    width: 120px;
    height: 120px;
    margin-bottom: 20px;
    border-radius: 20px;
    box-shadow: 0 0 30px rgba(0, 139, 128, 0.5);
}

.loader-text {
    font-size: 1.2rem;
    color: var(--accent-color-1);
    font-weight: 600;
    letter-spacing: 2px;
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* خلفية متحركة */
.animated-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
}

/* شريط التنقل */
.navbar, .header {
    position: fixed;
    top: 0;
    width: 100%;
    padding: 20px 5%;
    background: rgba(15, 15, 35, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    transition: var(--transition);
}

.nav-container, .header-content {
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
    justify-content: space-between;
    padding: 0 20px;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 15px;
    text-decoration: none;
    color: inherit;
    transition: var(--transition);
}

.logo {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 32px rgba(40, 144, 154, 0.3);
    overflow: hidden;
}

.logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.company-name {
    font-size: 26px;
    font-weight: bold;
    color: #ffffff;
}

/* أزرار */
.cta-button {
    background: linear-gradient(135deg, var(--accent-color-2), var(--accent-color-1));
    color: white;
    padding: 15px 30px;
    border: none;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: var(--transition);
    box-shadow: 0 8px 25px rgba(0, 139, 128, 0.3);
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0, 139, 128, 0.4);
}

/* محتوى رئيسي */
.main-content {
    padding-top: 100px;
    min-height: 100vh;
}

.section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 50px 5%;
}

/* تأثيرات الظهور */
.fade-in-element {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.fade-in-element.visible {
    opacity: 1;
    transform: translateY(0);
}

/* تحسينات الأداء */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

/* تحسين الخطوط */
@font-face {
    font-family: 'Segoe UI';
    font-display: swap;
}

/* تحسين الصور */
img {
    max-width: 100%;
    height: auto;
    loading: lazy;
}

/* تحسين الفيديو */
video {
    max-width: 100%;
    height: auto;
}

/* استعلامات الوسائط للأجهزة المحمولة */
@media (max-width: 768px) {
    .navbar, .header {
        padding: 15px 3%;
    }
    
    .nav-container, .header-content {
        padding: 0 10px;
    }
    
    .logo {
        width: 40px;
        height: 40px;
    }
    
    .company-name {
        font-size: 20px;
    }
    
    .section {
        padding: 30px 3%;
    }
    
    .cta-button {
        padding: 12px 24px;
        font-size: 1rem;
    }
}

/* Meteor Animation Only */
.meteor {
    position: absolute;
    width: 3px;
    height: 3px;
    background: white;
    box-shadow: 0 0 16px 6px white;
    transform: rotate(35deg);
    animation: shoot 6s linear infinite;
    opacity: 0;
    z-index: -1;
}

@keyframes shoot {
    0% {
        top: +0%;
        left: +0%;
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    50% {
        top: 60%;
        left: 70%;
        opacity: 0.6;
    }
    100% {
        top: 110%;
        left: 110%;
        opacity: 0;
    }
}
