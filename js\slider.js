document.addEventListener('DOMContentLoaded', function() {
    class Slider {
        constructor(selector) {
            this.slider = document.querySelector(selector);
            this.slides = this.slider.querySelectorAll('.slide');
            this.dotsContainer = this.slider.querySelector('.slider-dots');
            this.prevBtn = this.slider.querySelector('.slider-nav .prev');
            this.nextBtn = this.slider.querySelector('.slider-nav .next');
            this.currentSlide = 0;
            this.slideInterval = 5000; // 5 seconds
            this.autoSlide = true;
            this.isAnimating = false;
            this.animationDuration = 800; // Match CSS transition duration
            
            this.init();
        }

        init() {
            // Create dots
            this.slides.forEach((_, index) => {
                const dot = document.createElement('span');
                dot.classList.add('dot');
                if (index === 0) dot.classList.add('active');
                dot.addEventListener('click', () => this.goToSlide(index));
                this.dotsContainer.appendChild(dot);
            });
            
            this.dots = this.slider.querySelectorAll('.dot');
            
            // Event listeners
            if (this.prevBtn) {
                this.prevBtn.addEventListener('click', () => this.prevSlide());
            }
            
            if (this.nextBtn) {
                this.nextBtn.addEventListener('click', () => this.nextSlide());
            }
            
            // Touch events for mobile
            this.initTouchEvents();
            
            // Start auto slide
            if (this.autoSlide) {
                this.startAutoSlide();
            }
            
            // Pause on hover
            this.slider.addEventListener('mouseenter', () => this.pauseAutoSlide());
            this.slider.addEventListener('mouseleave', () => this.startAutoSlide());
            
            // Set initial active slide
            this.setActiveSlide(0);
        }
        
        initTouchEvents() {
            let touchStartX = 0;
            let touchEndX = 0;
            const touchThreshold = 50; // Minimum distance for swipe
            
            this.slider.addEventListener('touchstart', (e) => {
                touchStartX = e.changedTouches[0].screenX;
                this.pauseAutoSlide();
            }, { passive: true });
            
            this.slider.addEventListener('touchend', (e) => {
                touchEndX = e.changedTouches[0].screenX;
                this.handleSwipe(touchStartX, touchEndX, touchThreshold);
                this.startAutoSlide();
            }, { passive: true });
        }
        
        handleSwipe(startX, endX, threshold) {
            const difference = startX - endX;
            
            if (Math.abs(difference) < threshold) return;
            
            if (difference > 0) {
                this.nextSlide();
            } else {
                this.prevSlide();
            }
        }
        
        setActiveSlide(index) {
            if (this.isAnimating) return;
            
            this.isAnimating = true;
            
            // Update current slide index
            this.currentSlide = (index + this.slides.length) % this.slides.length;
            
            // Update active class on slides
            this.slides.forEach((slide, i) => {
                if (i === this.currentSlide) {
                    slide.classList.add('active');
                } else {
                    slide.classList.remove('active');
                }
            });
            
            // Update active dot
            this.dots.forEach((dot, i) => {
                if (i === this.currentSlide) {
                    dot.classList.add('active');
                } else {
                    dot.classList.remove('active');
                }
            });
            
            // Reset animation flag after transition
            setTimeout(() => {
                this.isAnimating = false;
            }, this.animationDuration);
        }
        
        nextSlide() {
            this.setActiveSlide(this.currentSlide + 1);
        }
        
        prevSlide() {
            this.setActiveSlide(this.currentSlide - 1);
        }
        
        goToSlide(index) {
            this.setActiveSlide(index);
        }
        
        startAutoSlide() {
            if (this.autoSlide) {
                clearInterval(this.autoSlideInterval);
                this.autoSlideInterval = setInterval(() => {
                    this.nextSlide();
                }, this.slideInterval);
            }
        }
        
        pauseAutoSlide() {
            clearInterval(this.autoSlideInterval);
        }
    }

    // Initialize the slider
    const slider = new Slider('.slider');
    
    // Handle window resize
    let resizeTimer;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(() => {
            // Reinitialize slider on resize to handle responsive changes
            slider.startAutoSlide();
        }, 250);
    });
});
