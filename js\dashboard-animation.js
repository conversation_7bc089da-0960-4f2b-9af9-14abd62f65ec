// نظام الشكل المتحرك للوحة التحكم
function setupDashboardAnimation() {
    // التحقق من وجود العناصر المطلوبة
    const dashboardContainer = document.querySelector('.dashboard-container');
    const imageOrbit = document.querySelector('.image-orbit');

    if (!dashboardContainer || !imageOrbit) {
        return; // لا توجد عناصر للتحريك
    }

    let currentRotation = 0;
    let isRotating = false;
    let animationFrame;
    const rotationSpeed = 0.5;

    function animate() {
        if (isRotating) {
            currentRotation += rotationSpeed;
            if (currentRotation >= 360) {
                currentRotation = currentRotation % 360;
            }
            imageOrbit.style.transform = `rotate(${currentRotation}deg)`;
            animationFrame = requestAnimationFrame(animate);
        }
    }

    // بدء الحركة عند مرور الماوس
    dashboardContainer.addEventListener('mouseenter', function() {
        if (!isRotating) {
            isRotating = true;
            animationFrame = requestAnimationFrame(animate);
        }
    });

    // إيقاف الحركة عند خروج الماوس
    dashboardContainer.addEventListener('mouseleave', function() {
        isRotating = false;
        cancelAnimationFrame(animationFrame);
    });
}

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    setupDashboardAnimation();
});

// تهيئة إضافية عند تحميل النافذة
window.addEventListener('load', function() {
    setupDashboardAnimation();
});

let isScrolling = false;
galleryWrapper.addEventListener('wheel', (e) => {
    e.preventDefault();
    if (!isScrolling) {
        isScrolling = true;
        galleryWrapper.scrollBy({
            top: e.deltaY,
            behavior: 'smooth'
        });
        setTimeout(() => { isScrolling = false; }, 300); // يمنع التكرار السريع
    }
});
