// ملف JavaScript مشترك للدوال المكررة
// تحسين الأداء بتجميع الدوال المشتركة

// إنشاء الجسيمات المتحركة (محسن للأداء)
function createParticles() {
    const particlesContainer = document.getElementById('particles');
    if (!particlesContainer) return;
    let particleCount = 25;
    // إذا كنا في الصفحة الرئيسية، قلل العدد
    if (window.location.pathname.endsWith('index.html') || window.location.pathname === '/' || window.location.pathname === '') {
        particleCount = 8;
    }
    const fragment = document.createDocumentFragment();
    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.top = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 6 + 's';
        particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
        fragment.appendChild(particle);
    }
    particlesContainer.appendChild(fragment);
}

// دالة تبديل اللغة المشتركة
function switchLanguage(lang) {
    sessionStorage.setItem('pageTransition', 'true');
    if (lang === 'en') {
        window.location.href = 'index-en.html';
    } else if (lang === 'ar') {
        window.location.href = 'index.html';
    }
}

// إدارة تحميل الصفحة المشتركة
function handlePageLoad() {
    const pageLoader = document.getElementById('pageLoader');
    const mainContent = document.getElementById('mainContent');
    if (!pageLoader || !mainContent) return;

    const isTransition = sessionStorage.getItem('pageTransition');

    if (isTransition) {
        sessionStorage.removeItem('pageTransition');
        setTimeout(() => {
            pageLoader.classList.add('fade-out');
            setTimeout(() => {
                pageLoader.style.display = 'none';
                showContentWithAnimation();
            }, 900);
        }, 2500); // مدة أطول لشاشة التحميل عند الانتقال بين اللغات
    } else {
        pageLoader.style.display = 'none';
        showContentWithAnimation();
    }
}

// إظهار المحتوى مع التأثيرات
function showContentWithAnimation() {
    const mainContent = document.getElementById('mainContent');
    if (!mainContent) return;

    mainContent.classList.remove('content-loading');
    mainContent.classList.add('content-loaded');

    setTimeout(addStaggerAnimation, 100);
}

// إضافة تأثير التتابع للعناصر
function addStaggerAnimation() {
    const elements = [
        '.hero-section',
        '.services-section h2',
        '.service-card',
        '.contact-section'
    ];

    elements.forEach((selector, index) => {
        const items = document.querySelectorAll(selector);
        items.forEach((item, itemIndex) => {
            setTimeout(() => {
                item.classList.add('stagger-animation');
            }, (index * 150) + (itemIndex * 80));
        });
    });
}

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', () => {
    handlePageLoad();
    initWebsite();
});

// تهيئة الموقع
function initWebsite() {
    createParticles();
    navbarScrollEffect();
    if (typeof ScrollAnimations !== 'undefined') {
        new ScrollAnimations();
    } else if (typeof scrollAnimations === 'function') {
        scrollAnimations();
    }
    setupScrollTop();
    randomizeMeteor();
    enhanceServiceCards();
    setInterval(randomizeMeteor, 5000);
}

// تأثير شريط التنقل عند التمرير
function navbarScrollEffect() {
    const navbar = document.querySelector('.navbar');
    if (!navbar) return;

    window.addEventListener('scroll', () => {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
}

// زر الانتقال للأعلى
function setupScrollTop() {
    const scrollTopBtn = document.getElementById('scrollTop');
    if (!scrollTopBtn) return;
    
    window.addEventListener('scroll', () => {
        if (window.scrollY > 500) {
            scrollTopBtn.classList.add('visible');
        } else {
            scrollTopBtn.classList.remove('visible');
        }
    });
    
    scrollTopBtn.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// تحسين تأثيرات hover للخدمات
function enhanceServiceCards() {
    const serviceCards = document.querySelectorAll('.service-card');
    if (!serviceCards.length) return;

    serviceCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) rotateX(5deg) scale(1.02)';
            
            const icon = this.querySelector('.service-icon i');
            if (icon) {
                icon.style.animation = 'iconFloat 2s ease-in-out infinite';
            }
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) rotateX(0) scale(1)';
            
            const icon = this.querySelector('.service-icon i');
            if (icon) {
                icon.style.animation = '';
            }
        });
    });
}

// تحسين الأداء بـ debounce للأحداث
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// تحسين الأداء بـ throttle للأحداث
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// تحميل الموارد بطريقة lazy
function lazyLoadResource(src, type = 'script') {
    return new Promise((resolve, reject) => {
        const element = document.createElement(type);
        element.onload = resolve;
        element.onerror = reject;
        
        if (type === 'script') {
            element.src = src;
            element.async = true;
        } else if (type === 'link') {
            element.href = src;
            element.rel = 'stylesheet';
        }
        
        document.head.appendChild(element);
    });
}

// تحسين الصور بـ lazy loading
function setupLazyImages() {
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                observer.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));
}

// تهيئة مشتركة للصفحات
function initCommon() {
    handlePageLoad();
    createParticles();
    setupLazyImages();
}

// تشغيل التهيئة عند تحميل الصفحة
window.addEventListener('load', initCommon);

// تصدير الدوال للاستخدام العام
window.MCTCommon = {
    createParticles,
    switchLanguage,
    handlePageLoad,
    debounce,
    throttle,
    lazyLoadResource,
    setupLazyImages,
    initCommon
};
