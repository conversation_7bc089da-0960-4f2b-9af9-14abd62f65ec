// Meteor Animation Only\n(function(){\n  function randomizeMeteor() {\n    const meteors = document.querySelectorAll('.meteor');\n    meteors.forEach(meteor => {\n      meteor.style.top = Math.random() * 8 + '%';\n      meteor.style.left = Math.random() * 8 + '%';\n      meteor.style.animationDelay = Math.random() * 1 + 's';\n    });\n  }\n  if(document.querySelector('.meteor')) {\n    randomizeMeteor();\n    setInterval(randomizeMeteor, 5000);\n  }\n})(); 