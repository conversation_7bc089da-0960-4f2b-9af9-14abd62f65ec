<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الدعم والصيانة - MCT</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* Font Application for Maintenance Page */
        * {
            font-family: 'NotoNaskhArabic', 'Arial', sans-serif;
        }
        
        body {
            font-family: 'NotoNaskhArabic', 'Arial', sans-serif;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: 'NotoNaskhArabic', 'Arial', sans-serif;
            font-weight: 700;
        }
        
        .cta-button, button {
            font-family: 'NotoNaskhArabic', 'Arial', sans-serif;
            font-weight: 600;
        }
        
        .nav-links a {
            font-family: 'NotoNaskhArabic', 'Arial', sans-serif;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- شاشة التحميل -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-logo">
            <img src="image/logo-8.png" alt="MCT Logo">
            <div class="loader-text">Support & Maintenance</div>
        </div>
    </div>

    <div class="animated-bg"></div>
    <div class="floating-particles" id="particles"></div>
    <header class="header">
        <div class="header-content">
            <a href="index.html" class="logo-section">
                <div class="logo">
                    <img src="image/logo-11.png" alt="MCT Logo">
                </div>
                <div class="logo">
                    <img src="image/logo-10.png" alt="MCT Logo">
                </div>
            </a>
            <div class="nav-center">
                <ul class="service-nav">
                    <li><a href="networking.html">حلول الشبكات</a></li>
                    <li><a href="software.html">تطوير البرمجيات</a></li>
                    <li><a href="cybersecurity.html">أمن المعلومات</a></li>
                    <!--<li><a href="security-assessment.html">الفحص الأمني</a></li>--> 
                    <li><a href="maintenance.html" class="active">الدعم والصيانة</a></li>
                </ul>
            </div>
            <div class="nav-right">
                <button id="lang-toggle" onclick="switchLanguage('en')">EN</button>
                <a href="index.html" class="back-btn">العودة للرئيسية</a>
            </div>
        </div>
    </header>
    <main class="main-content">
        <!-- Service Hero -->
        <section class="service-hero fade-in-element">
            <div class="service-icon"><i class="fas fa-tools"></i></div>
            <h1 class="service-title fade-in-element delay-1">صيانة الشبكات</h1>
            <p class="service-subtitle fade-in-element delay-2">
                نقدم خدمات صيانة شاملة للشبكات لضمان استمرارية عملها وكفاءتها. فريقنا من الخبراء يضمن صيانة دورية ووقائية لشبكتك.
            </p>
        </section>

        <!-- خدماتنا -->
        <section class="content-section fade-in-element delay-3">
            <h2 class="section-title fade-in-element">خدمات الصيانة لدينا</h2>
            <ul class="services-list fade-in-element delay-1">
                <li><strong>الصيانة الوقائية:</strong> فحص دوري للشبكة وتحديث البرامج والأجهزة</li>
                <li><strong>إصلاح الأعطال:</strong> معالجة سريعة وفعالة لأي مشاكل في الشبكة</li>
                <li><strong>تحديثات النظام:</strong> تثبيت وتحديث برامج وأجهزة الشبكة</li>
                <li><strong>مراقبة الأداء:</strong> متابعة مستمرة لأداء الشبكة وكشف المشاكل مبكراً</li>
                <li><strong>نسخ احتياطي:</strong> حماية بيانات الشبكة من خلال نسخ احتياطي منتظم</li>
                <li><strong>تدريب الموظفين:</strong> تدريب فريق العمل على التعامل مع الشبكة</li>
                <li><strong>تقارير الأداء:</strong> تقارير دورية عن حالة الشبكة وأدائها</li>
            </ul>
        </section>

        <section class="content-section fade-in-element delay-4">
            <h2 class="section-title fade-in-element">لماذا تختار خدماتنا؟</h2>
            <div class="features-grid">
                <div class="feature-card fade-in-element delay-1">
                    <h3>خبرة واسعة</h3>
                    <p>فريق من الخبراء المتخصصين في صيانة الشبكات</p>
                </div>
                <div class="feature-card fade-in-element delay-2">
                    <h3>دعم فوري</h3>
                    <p>استجابة سريعة لطلبات الصيانة والدعم الفني</p>
                </div>
                <div class="feature-card fade-in-element delay-3">
                    <h3>حلول متكاملة</h3>
                    <p>خدمات صيانة شاملة تغطي جميع جوانب الشبكة</p>
                </div>
                <div class="feature-card fade-in-element delay-4">
                    <h3>تقنيات متطورة</h3>
                    <p>استخدام أحدث التقنيات والأدوات في الصيانة</p>
                </div>
                <div class="feature-card fade-in-element delay-5">
                    <h3>تكلفة تنافسية</h3>
                    <p>خدمات صيانة فعالة بأسعار منافسة تناسب مختلف الميزانيات</p>
                </div>
                <div class="feature-card fade-in-element delay-6">
                    <h3>ضمان الجودة</h3>
                    <p>التزام بمعايير الجودة العالمية في خدمات الصيانة</p>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="contact-cta fade-in-element delay-5">
            <h2 style="color: #008B80; margin-bottom: 20px;">هل تحتاج إلى صيانة لشبكتك؟</h2>
            <p style="margin-bottom: 30px; color: #c0c0c0;">تواصل معنا اليوم للحصول على استشارة مجانية وتقييم شامل لاحتياجاتك</p>
            <a href="index.html#contact" class="cta-button">تواصل معنا</a>
            <a href="tel:+966123456789" class="cta-button">اتصل بنا</a>
        </section>
    </main>
    <script>
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 30;
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }
        // تأثير التبديل السلس للغة
        function switchLanguage(lang) {
            if (lang === 'en') {
                sessionStorage.setItem('pageTransition', 'true');
                window.location.href = 'maintenance-en.html';
            }
        }

        // إدارة تحميل الصفحة
        function handlePageLoad() {
            const pageLoader = document.getElementById('pageLoader');
            const isTransition = sessionStorage.getItem('pageTransition');

            if (isTransition) {
                sessionStorage.removeItem('pageTransition');
                setTimeout(() => pageLoader.classList.add('fade-out'), 2000);
                setTimeout(() => pageLoader.style.display = 'none', 2800);
            } else {
                pageLoader.style.display = 'none';
            }
        }

        window.addEventListener('load', () => {
            handlePageLoad();
            createParticles();
        });

    </script>
    <script src="js/dashboard-animation.js"></script>
    <script src="js/scroll-animations.js"></script>
</body>
</html>
