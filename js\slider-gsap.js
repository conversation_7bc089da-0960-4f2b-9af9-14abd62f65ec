// GSAP Slider Animation (Reusable)
(function(){
  const slider = document.querySelector('.slider');
  if (!slider) return;

  const slidesWrapper = slider.querySelector('.slides-wrapper');
  const slides = slider.querySelectorAll('.slide');
  const dotsContainer = slider.querySelector('.dots');
  let currentIndex = 0;
  const totalSlides = slides.length;
  let animating = false;
  let autoPlayInterval;

  // إنشاء النقاط (dots)
  dotsContainer.innerHTML = '';
  slides.forEach((_, i) => {
    const dot = document.createElement('div');
    dot.classList.add('dot');
    if (i === 0) dot.classList.add('active');
    dot.addEventListener('click', () => {
      if (i !== currentIndex && !animating) {
        goToSlide(i);
      }
    });
    dotsContainer.appendChild(dot);
  });

  function updateDots(index) {
    const dots = dotsContainer.querySelectorAll('.dot');
    dots.forEach(dot => dot.classList.remove('active'));
    if (dots[index]) dots[index].classList.add('active');
  }

  function goToSlide(newIndex) {
    if (animating || newIndex === currentIndex) return;
    animating = true;

    const currentSlide = slides[currentIndex];
    const nextSlide    = slides[newIndex];
    const direction    = newIndex > currentIndex ? 1 : -1;
    const offsetSign  = document.dir === 'rtl' ? -direction : direction;

    // حركة الشريحة الحالية للخارج
    gsap.to(currentSlide, {
      xPercent: -100 * direction,
      duration: 0.6,
      ease: "power2.inOut"
    });

    // إخفاء نصوص الشريحة الحالية (إن وجدت)
    const currentTexts = currentSlide.querySelectorAll('.slide-content > *');
    if (currentTexts.length) {
      gsap.to(currentTexts, {
        y: 30,
        opacity: 0,
        stagger: 0.05,
        duration: 0.3,
        ease: "power2.in"
      });
    }

    // إعداد الشريحة القادمة خارج الشاشة
    gsap.set(nextSlide, { xPercent: 100 * direction });
    // حركتها للداخل
    gsap.to(nextSlide, {
      xPercent: 0,
      duration: 0.6,
      ease: "power2.inOut",
      onComplete: () => {
        animating = false;
      }
    });

    // إظهار نصوص الشريحة الجديدة (إن وجدت)
    const nextTexts = nextSlide.querySelectorAll('.slide-content > *');
    if (nextTexts.length) {
      gsap.fromTo(nextTexts,
        { y: 30, opacity: 0 },
        {
          y: 0,
          opacity: 1,
          stagger: 0.1,
          duration: 0.4,
          ease: "power2.out"
        }
      );
    }

    currentIndex = newIndex;
    updateDots(newIndex);
  }

  // التشغيل التلقائي
  function autoPlay() {
    autoPlayInterval = setInterval(() => {
      goToSlide((currentIndex + 1) % totalSlides);
    }, 5000);
  }
  slider.addEventListener('mouseenter', () => clearInterval(autoPlayInterval));
  slider.addEventListener('mouseleave', autoPlay);

  // بدء الحالة الابتدائية بعد تحميل الصفحة
  window.addEventListener('load', () => {
    // إبقاء كل الشرائح خارج العرض
    gsap.set(slides, { xPercent: 100 });
    // إظهار الشريحة الأولى بدون حركة
    gsap.set(slides[0], { xPercent: 0 });
    // وظهور نصوصها
    const firstTexts = slides[0].querySelectorAll('.slide-content > *');
    if (firstTexts.length) {
      gsap.set(firstTexts, { y: 0, opacity: 1 });
    }
  });

  // إعادة الحساب عند تغيير حجم الشاشة
  window.addEventListener('resize', () => {
    const slideWidth = slides[0].offsetWidth + parseInt(getComputedStyle(slides[0]).marginRight);
    const x = slideWidth * currentIndex * (document.dir === 'rtl' ? 1 : -1);
    gsap.set(slidesWrapper, { x });
  });

  // إطلاق التشغيل التلقائي
  autoPlay();

})();
