// نظام التمرير السلس والظهور التدريجي للعناصر
class ScrollAnimations {
    constructor() {
        this.init();
    }

    init() {
        // إعداد التمرير السلس
        this.setupSmoothScrolling();
        
        // إعداد الظهور التدريجي
        this.setupFadeInAnimations();
        
        // إضافة classes للعناصر تلقائياً
        this.addAnimationClasses();
    }

    setupSmoothScrolling() {
        // التمرير السلس للروابط الداخلية
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    setupFadeInAnimations() {
        // مراقب التقاطع للظهور التدريجي مع تحسين الأداء
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px',
            root: null
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                    // إزالة المراقبة بعد الظهور لتحسين الأداء
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // مراقبة العناصر بشكل أكثر كفاءة
        const elements = document.getElementsByClassName('fade-in-element');
        for (let i = 0; i < elements.length; i++) {
            observer.observe(elements[i]);
        }
    }

    addAnimationClasses() {
        // إضافة classes تلقائياً للعناصر الرئيسية
        const elementsToAnimate = [
            { selector: '.service-hero', delay: 1 },
            { selector: '.content-section', delay: 2 },
            { selector: '.feature-card', delay: 'staggered' },
            { selector: '.contact-cta', delay: 4 }
        ];

        elementsToAnimate.forEach(({ selector, delay }) => {
            const elements = document.querySelectorAll(selector);
            elements.forEach((element, index) => {
                // تجاهل العناصر التي تحتوي بالفعل على fade-in-element
                if (!element.classList.contains('fade-in-element')) {
                    element.classList.add('fade-in-element');

                    if (delay === 'staggered') {
                        // تأخير متدرج للعناصر المتعددة
                        element.classList.add(`delay-${(index % 6) + 1}`);
                    } else {
                        element.classList.add(`delay-${delay}`);
                    }

                    // إظهار العنصر فور<|im_start|> إذا كان في منطقة الرؤية
                    const rect = element.getBoundingClientRect();
                    if (rect.top < window.innerHeight && rect.bottom > 0) {
                        setTimeout(() => {
                            element.classList.add('visible');
                        }, delay === 'staggered' ? (index * 100) : (delay * 100));
                    }
                }
            });
        });
    }

    // إضافة تأثيرات إضافية للعناصر
    addHoverEffects() {
        // تأثيرات hover للبطاقات
        document.querySelectorAll('.feature-card, .service-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-10px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) scale(1)';
            });
        });
    }

    // تأثير الكتابة التدريجية للعناوين
    typewriterEffect(element, text, speed = 50) {
        element.textContent = '';
        let i = 0;
        
        const timer = setInterval(() => {
            if (i < text.length) {
                element.textContent += text.charAt(i);
                i++;
            } else {
                clearInterval(timer);
            }
        }, speed);
    }

    // تأثير العد التصاعدي للأرقام
    countUpEffect(element, target, duration = 2000) {
        const start = 0;
        const increment = target / (duration / 16);
        let current = start;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                element.textContent = target;
                clearInterval(timer);
            } else {
                element.textContent = Math.floor(current);
            }
        }, 16);
    }

    // تأثير التموج عند النقر
    rippleEffect(element) {
        element.addEventListener('click', (e) => {
            const ripple = document.createElement('span');
            const rect = element.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s ease-out;
                pointer-events: none;
            `;
            
            element.style.position = 'relative';
            element.style.overflow = 'hidden';
            element.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    }

    // تأثير الشحن التدريجي للشرائط
    progressBarEffect(element, percentage, duration = 1500) {
        element.style.width = '0%';
        element.style.transition = `width ${duration}ms ease-out`;
        
        setTimeout(() => {
            element.style.width = percentage + '%';
        }, 100);
    }

    // تأثير الاهتزاز للتنبيهات
    shakeEffect(element) {
        element.style.animation = 'shake 0.5s ease-in-out';
        setTimeout(() => {
            element.style.animation = '';
        }, 500);
    }

    // تأثير النبض للعناصر المهمة
    pulseEffect(element, duration = 2000) {
        element.style.animation = `pulse ${duration}ms infinite`;
    }

    // تأثير التدوير للتحميل
    spinEffect(element) {
        element.style.animation = 'spin 1s linear infinite';
    }

    // إيقاف جميع التأثيرات
    stopAllEffects(element) {
        element.style.animation = '';
        element.style.transform = '';
        element.style.transition = '';
    }
}

// إضافة CSS للتأثيرات الإضافية
const additionalStyles = `
@keyframes ripple {
    to {
        transform: scale(2);
        opacity: 0;
    }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.05); opacity: 0.8; }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}
`;

// إضافة الأنماط إلى الصفحة
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    const scrollAnimations = new ScrollAnimations();
    
    // إضافة تأثيرات hover للبطاقات
    scrollAnimations.addHoverEffects();
    
    // إضافة تأثير التموج للأزرار
    document.querySelectorAll('.cta-button, .back-btn').forEach(button => {
        scrollAnimations.rippleEffect(button);
    });
    
    // إضافة class hover-lift للبطاقات
    document.querySelectorAll('.feature-card, .service-card').forEach(card => {
        card.classList.add('hover-lift');
    });
});

// تصدير الكلاس للاستخدام في ملفات أخرى
window.ScrollAnimations = ScrollAnimations;
