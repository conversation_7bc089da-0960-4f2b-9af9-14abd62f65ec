 /* ===================================
   VERTICAL SLIDER SECTION
   ===================================*/
.vertical-slider-section {
    padding: 80px 0;
    background: linear-gradient(135deg, rgba(15, 15, 35, 0.8), rgba(26, 26, 46, 0.8));
    margin: 50px 0;
    position: relative;
    overflow: hidden;
}

/* Additional decorative images */
.vertical-slider-section .decorative-image {
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(0, 139, 128, 0.2), rgba(0, 139, 128, 0.05));
    border: 2px solid var(--accent-color-2);
    box-shadow: 0 0 20px rgba(0, 139, 128, 0.3),
                inset 0 0 20px rgba(0, 139, 128, 0.1);
    z-index: 1;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    cursor: pointer;
    transition: all 0.3s ease;
}

.vertical-slider-section .decorative-image:hover {
    transform: scale(1.1);
    box-shadow: 0 0 30px rgba(0, 139, 128, 0.5),
                inset 0 0 30px rgba(0, 139, 128, 0.2);
    border-color: var(--accent-color-1);
}

.vertical-slider-section .decorative-image img {
    width: 40px;
    height: 40px;
    object-fit: contain;
    filter: drop-shadow(0 0 10px rgba(0, 139, 128, 0.5));
    transition: all 0.3s ease;
}

.vertical-slider-section .decorative-image:hover img {
    transform: scale(1.2) rotate(10deg);
    filter: drop-shadow(0 0 15px rgba(0, 139, 128, 0.8));
}

/* Floating Image 1 - Left Edge */
.vertical-slider-section .floating-image-1 {
    left: 0;
    top: 0;
    animation: edgeOrbitCW 28s linear infinite;
}

/* Floating Image 2 - Right Edge */
.vertical-slider-section .floating-image-2 {
    left: 0;
    top: calc(100% - 60px);
    animation: edgeOrbitCCW 28s linear infinite;
}

@keyframes edgeOrbitCW {
    0%   { top: 0; left: 0; }
    24.9%  { top: 0; left: calc(100% - 60px); }
    25%  { top: 0; left: calc(100% - 60px); }
    49.9%  { top: calc(100% - 60px); left: calc(100% - 60px); }
    50%  { top: calc(100% - 60px); left: calc(100% - 60px); }
    74.9%  { top: calc(100% - 60px); left: 0; }
    75%  { top: calc(100% - 60px); left: 0; }
    99.9% { top: 0; left: 0; }
    100% { top: 0; left: 0; }
}
@keyframes edgeOrbitCCW {
    0%   { top: calc(100% - 60px); left: 0; }
    24.9%  { top: 0; left: 0; }
    25%  { top: 0; left: 0; }
    49.9%  { top: 0; left: calc(100% - 60px); }
    50%  { top: 0; left: calc(100% - 60px); }
    74.9%  { top: calc(100% - 60px); left: calc(100% - 60px); }
    75%  { top: calc(100% - 60px); left: calc(100% - 60px); }
    99.9% { top: calc(100% - 60px); left: 0; }
    100% { top: calc(100% - 60px); left: 0; }
}

.vertical-content-container {
    display: flex;
    gap: 60px;
    align-items: flex-start;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 50px;
}

.vertical-text-content {
    flex: 1;
    position: relative;
    height: 600px;
    overflow: hidden;
}

.text-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
    padding: 40px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.text-slide.active {
    opacity: 1;
    transform: translateY(0);
}

.text-slide h3 {
    font-size: 2rem;
    color: var(--accent-color-1);
    margin-bottom: 20px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.text-slide p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #e0e0e0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.vertical-slider-container {
    flex: 1;
    position: relative;
}

.gallery-container {
    position: relative;
    width: 100%;
    height: 600px;
    border-radius: 20px;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.gallery-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(to bottom, rgba(15, 15, 35, 0.8), transparent);
    z-index: 2;
    pointer-events: none;
}

.gallery-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(to top, rgba(15, 15, 35, 0.8), transparent);
    z-index: 2;
    pointer-events: none;
}

.gallery-wrapper {
    height: 100%;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.gallery-wrapper::-webkit-scrollbar {
    display: none;
}

.gallery-grid {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 30px;
}

.image-card {
    width: 100%;
    height: 535px;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
    transition: all 0.3s ease;
    cursor: pointer;
}

.image-card img {
    width: 100%;
    height: 535px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.image-card:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
}

.image-card:hover img {
    transform: scale(1.1);
}

/* RTL Support for Arabic */
body[dir="rtl"] .vertical-content-container {
    flex-direction: row-reverse;
}

body[dir="rtl"] .text-slide {
    text-align: right;
}

/* Responsive Design for Vertical Slider */
@media (max-width: 1400px) {
    .vertical-content-container {
        gap: 40px;
        padding: 0 30px;
    }
    
    .vertical-text-content,
    .vertical-slider-container {
        flex: 1;
    }
    
    .vertical-text-content {
        height: 500px;
    }
    
    .gallery-container {
        height: 500px;
    }
    
    body[dir="rtl"] .vertical-content-container {
        flex-direction: row-reverse;
    }
    
    .vertical-slider-section .decorative-image {
        width: 50px;
        height: 50px;
    }
    
    .vertical-slider-section .decorative-image img {
        width: 35px;
        height: 35px;
    }
    
    .vertical-slider-section .floating-image-1 {
        left: 15px;
    }
    
    .vertical-slider-section .floating-image-2 {
        right: 15px;
    }
}

@media (max-width: 992px) {
    .vertical-content-container {
        flex-direction: column;
        gap: 30px;
    }
    
    .vertical-text-content {
        height: 300px;
    }
    
    .gallery-container {
        height: 400px;
    }
    
    body[dir="rtl"] .vertical-content-container {
        flex-direction: column;
    }
    
    .vertical-slider-section .decorative-image {
        width: 45px;
        height: 45px;
    }
    
    .vertical-slider-section .decorative-image img {
        width: 30px;
        height: 30px;
    }
    
    .vertical-slider-section .floating-image-1 {
        left: 10px;
    }
    
    .vertical-slider-section .floating-image-2 {
        right: 10px;
    }
}

@media (max-width: 768px) {
    .vertical-content-container {
        padding: 0 20px;
    }
    
    .vertical-text-content {
        height: 250px;
        padding: 20px;
    }
    
    .gallery-container {
        height: 350px;
    }
    
    .text-slide h3 {
        font-size: 1.5rem;
    }
    
    .text-slide p {
        font-size: 1rem;
    }
    
    .vertical-slider-section .decorative-image {
        width: 40px;
        height: 40px;
    }
    
    .vertical-slider-section .decorative-image img {
        width: 25px;
        height: 25px;
    }
    
    .vertical-slider-section .floating-image-1 {
        left: 8px;
    }
    
    .vertical-slider-section .floating-image-2 {
        right: 8px;
    }
}

@media (max-width: 480px) {
    .vertical-content-container {
        padding: 0 15px;
    }
    
    .vertical-text-content {
        height: 200px;
        padding: 15px;
    }
    
    .gallery-container {
        height: 300px;
    }
    
    .text-slide h3 {
        font-size: 1.3rem;
        margin-bottom: 15px;
    }
    
    .text-slide p {
        font-size: 0.9rem;
        line-height: 1.6;
    }
    
    .vertical-slider-section .decorative-image {
        width: 35px;
        height: 35px;
    }
    
    .vertical-slider-section .decorative-image img {
        width: 20px;
        height: 20px;
    }
    
    .vertical-slider-section .floating-image-1 {
        left: 5px;
    }
    
    .vertical-slider-section .floating-image-2 {
        right: 5px;
    }
}